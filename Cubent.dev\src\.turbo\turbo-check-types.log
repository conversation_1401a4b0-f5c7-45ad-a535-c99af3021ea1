 WARN  Unsupported engine: wanted: {"node":"20.19.2"} (current: {"node":"v22.14.0","pnpm":"10.8.1"})

> cubent@0.31.9 check-types C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\src
> tsc --noEmit

activate/registerCommands.ts(173,2): error TS2353: Object literal may only specify known properties, and 'toggleAutoApprove' does not exist in type 'Record<"newTask" | "activationCompleted" | "plusButtonClicked" | "promptsButtonClicked" | "mcpButtonClicked" | "historyButtonClicked" | "popoutButtonClicked" | "accountButtonClicked" | ... 10 more ... | "acceptInput", any>'.
activate/registerCommands.ts(184,46): error TS2339: Property 'autoApprovalEnabled' does not exist on type 'Promise<{ apiConfiguration: { reasoningEffort?: "low" | "medium" | "high" | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | "vscode-lm" | ... 13 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }; ... 74 more ...; codebase...'.
activate/registerCommands.ts(200,19): error TS2339: Property 'updateState' does not exist on type 'ClineProvider'.
api/providers/__tests__/anthropic-vertex.test.ts(696,5): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type 'ApiHandlerOptions'.
api/providers/__tests__/anthropic-vertex.test.ts(710,5): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type 'ApiHandlerOptions'.
api/providers/__tests__/anthropic.test.ts(69,4): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type 'ApiHandlerOptions'.
api/providers/__tests__/anthropic.test.ts(87,5): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type 'ApiHandlerOptions'.
api/providers/__tests__/anthropic.test.ts(237,5): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type 'ApiHandlerOptions'.
api/providers/__tests__/anthropic.test.ts(251,5): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type 'ApiHandlerOptions'.
api/providers/__tests__/gemini.test.ts(21,4): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type 'GeminiHandlerOptions'.
api/providers/base-openai-compatible-provider.ts(54,21): error TS2339: Property 'apiKey' does not exist on type 'ApiHandlerOptions'.
api/providers/base-openai-compatible-provider.ts(60,25): error TS2339: Property 'apiKey' does not exist on type 'ApiHandlerOptions'.
api/providers/chutes.ts(13,4): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type 'BaseOpenAiCompatibleProviderOptions<ChutesModelId>'.
api/providers/groq.ts(13,4): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type 'BaseOpenAiCompatibleProviderOptions<GroqModelId>'.
core/autocomplete/__tests__/CubentAutocompleteProvider.test.ts(105,5): error TS2345: Argument of type 'AbortSignal' is not assignable to parameter of type 'CancellationToken'.
  Type 'AbortSignal' is missing the following properties from type 'CancellationToken': isCancellationRequested, onCancellationRequested
core/autocomplete/__tests__/CubentAutocompleteProvider.test.ts(157,5): error TS2345: Argument of type 'AbortSignal' is not assignable to parameter of type 'CancellationToken'.
  Type 'AbortSignal' is missing the following properties from type 'CancellationToken': isCancellationRequested, onCancellationRequested
core/autocomplete/__tests__/CubentAutocompleteProvider.test.ts(181,5): error TS2345: Argument of type 'AbortSignal' is not assignable to parameter of type 'CancellationToken'.
  Type 'AbortSignal' is missing the following properties from type 'CancellationToken': isCancellationRequested, onCancellationRequested
core/autocomplete/context/ImportDefinitionsService.ts(59,23): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
core/autocomplete/context/WorkspaceContextService.ts(320,27): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
core/autocomplete/providers/MistralAutocompleteProvider.ts(54,35): error TS2345: Argument of type '{ readonly prefix: "[PREFIX]"; readonly suffix: "[SUFFIX]"; readonly middle: ""; readonly stopTokens: readonly ["[PREFIX]", "[SUFFIX]"]; }' is not assignable to parameter of type 'FIMTemplate'.
  Types of property 'stopTokens' are incompatible.
    The type 'readonly ["[PREFIX]", "[SUFFIX]"]' is 'readonly' and cannot be assigned to the mutable type 'string[]'.
core/autocomplete/providers/OllamaAutocompleteProvider.ts(52,35): error TS2345: Argument of type '{ readonly prefix: "<|fim_prefix|>"; readonly suffix: "<|fim_suffix|>"; readonly middle: "<|fim_middle|>"; readonly stopTokens: readonly ["<|endoftext|>", "<|fim_prefix|>", "<|fim_middle|>", "<|fim_suffix|>", ... 4 more ..., "<|im_end|>"]; }' is not assignable to parameter of type 'FIMTemplate'.
  Types of property 'stopTokens' are incompatible.
    The type 'readonly ["<|endoftext|>", "<|fim_prefix|>", "<|fim_middle|>", "<|fim_suffix|>", "<|fim_pad|>", "<|repo_name|>", "<|file_sep|>", "<|im_start|>", "<|im_end|>"]' is 'readonly' and cannot be assigned to the mutable type 'string[]'.
core/config/__tests__/ContextProxy.test.ts(185,28): error TS2345: Argument of type '"apiKey"' is not assignable to parameter of type '"anthropicApiKey" | "glamaApiKey" | "openRouterApiKey" | "awsAccessKey" | "awsSecretKey" | "awsSessionToken" | "openAiApiKey" | "geminiApiKey" | "openAiNativeApiKey" | "mistralApiKey" | ... 8 more ... | "codeIndexQdrantApiKey"'.
core/config/__tests__/ContextProxy.test.ts(188,35): error TS2345: Argument of type '"apiKey"' is not assignable to parameter of type '"anthropicApiKey" | "glamaApiKey" | "openRouterApiKey" | "awsAccessKey" | "awsSecretKey" | "awsSessionToken" | "openAiApiKey" | "geminiApiKey" | "openAiNativeApiKey" | "mistralApiKey" | ... 8 more ... | "codeIndexQdrantApiKey"'.
core/config/__tests__/ContextProxy.test.ts(195,28): error TS2345: Argument of type '"apiKey"' is not assignable to parameter of type '"anthropicApiKey" | "glamaApiKey" | "openRouterApiKey" | "awsAccessKey" | "awsSecretKey" | "awsSessionToken" | "openAiApiKey" | "geminiApiKey" | "openAiNativeApiKey" | "mistralApiKey" | ... 8 more ... | "codeIndexQdrantApiKey"'.
core/config/__tests__/ContextProxy.test.ts(201,46): error TS2345: Argument of type '"apiKey"' is not assignable to parameter of type '"anthropicApiKey" | "glamaApiKey" | "openRouterApiKey" | "awsAccessKey" | "awsSecretKey" | "awsSessionToken" | "openAiApiKey" | "geminiApiKey" | "openAiNativeApiKey" | "mistralApiKey" | ... 8 more ... | "codeIndexQdrantApiKey"'.
core/config/__tests__/ContextProxy.test.ts(206,28): error TS2345: Argument of type '"apiKey"' is not assignable to parameter of type '"anthropicApiKey" | "glamaApiKey" | "openRouterApiKey" | "awsAccessKey" | "awsSecretKey" | "awsSessionToken" | "openAiApiKey" | "geminiApiKey" | "openAiNativeApiKey" | "mistralApiKey" | ... 8 more ... | "codeIndexQdrantApiKey"'.
core/config/__tests__/ContextProxy.test.ts(212,46): error TS2345: Argument of type '"apiKey"' is not assignable to parameter of type '"anthropicApiKey" | "glamaApiKey" | "openRouterApiKey" | "awsAccessKey" | "awsSecretKey" | "awsSessionToken" | "openAiApiKey" | "geminiApiKey" | "openAiNativeApiKey" | "mistralApiKey" | ... 8 more ... | "codeIndexQdrantApiKey"'.
core/config/__tests__/ContextProxy.test.ts(396,28): error TS2345: Argument of type '"apiKey"' is not assignable to parameter of type '"anthropicApiKey" | "glamaApiKey" | "openRouterApiKey" | "awsAccessKey" | "awsSecretKey" | "awsSessionToken" | "openAiApiKey" | "geminiApiKey" | "openAiNativeApiKey" | "mistralApiKey" | ... 8 more ... | "codeIndexQdrantApiKey"'.
core/config/__tests__/ProviderSettingsManager.test.ts(273,5): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | "vscode-lm" | "lmstudio" | ... 12 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/__tests__/ProviderSettingsManager.test.ts(312,7): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/__tests__/ProviderSettingsManager.test.ts(325,5): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | "vscode-lm" | "lmstudio" | ... 12 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/__tests__/ProviderSettingsManager.test.ts(431,7): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(244,5): error TS1117: An object literal cannot have multiple properties with the same name.
core/config/ProviderSettingsManager.ts(250,5): error TS1117: An object literal cannot have multiple properties with the same name.
core/config/ProviderSettingsManager.ts(687,5): error TS1117: An object literal cannot have multiple properties with the same name.
core/config/ProviderSettingsManager.ts(693,5): error TS1117: An object literal cannot have multiple properties with the same name.
core/config/ProviderSettingsManager.ts(872,16): error TS2339: Property 'apiKey' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(883,16): error TS2339: Property 'geminiBaseUrl' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(885,16): error TS2339: Property 'xaiBaseUrl' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(932,41): error TS2339: Property 'geminiBaseUrl' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/config/ProviderSettingsManager.ts(935,74): error TS2339: Property 'xaiBaseUrl' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/task/__tests__/Task.test.ts(240,4): error TS2353: Object literal may only specify known properties, and 'apiKey' does not exist in type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | "vscode-lm" | "lmstudio" | ... 12 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/task/Task.ts(393,42): error TS2341: Property 'getGlobalState' is private and only accessible within class 'ClineProvider'.
core/task/Task.ts(1402,8): error TS2322: Type 'undefined' is not assignable to type 'boolean'.
core/task/Task.ts(1515,44): error TS2339: Property 'apiConfigName' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | "vscode-lm" | "lmstudio" | ... 12 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/task/Task.ts(1523,44): error TS2345: Argument of type '{ modelId: string; cubentUnitsUsed: number; tokensUsed: number; inputTokens: number; outputTokens: number; costAccrued: number; requestsMade: number; sessionId: string; metadata: { provider: string; ... 4 more ...; timestamp: number; }; }' is not assignable to parameter of type 'CubentUsageEntry'.
  Property 'userId' is missing in type '{ modelId: string; cubentUnitsUsed: number; tokensUsed: number; inputTokens: number; outputTokens: number; costAccrued: number; requestsMade: number; sessionId: string; metadata: { provider: string; ... 4 more ...; timestamp: number; }; }' but required in type 'CubentUsageEntry'.
core/tools/executeCommandTool.ts(85,44): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error'.
core/user/UsageTrackingService.ts(4,42): error TS2307: Cannot find module '@shared/api' or its corresponding type declarations.
core/user/UserManagementIntegration.ts(3,42): error TS2307: Cannot find module '@shared/api' or its corresponding type declarations.
core/user/UserManagementIntegration.ts(7,29): error TS2307: Cannot find module '../../packages/cloud/src/AuthService' or its corresponding type declarations.
core/user/UserManagementService.ts(16,29): error TS2307: Cannot find module '../../packages/cloud/src/AuthService' or its corresponding type declarations.
core/user/UserManagementService.ts(70,24): error TS2554: Expected 0 arguments, but got 1.
core/webview/__tests__/ClineProvider.test.ts(2086,27): error TS2345: Argument of type '"apiKey"' is not assignable to parameter of type '"reasoningEffort" | "currentApiConfigName" | "apiProvider" | "listApiConfigMeta" | "pinnedApiConfigs" | "lastShownAnnouncementId" | "customInstructions" | "taskHistory" | ... 139 more ... | "codeIndexQdrantApiKey"'.
core/webview/ClineProvider.ts(169,26): error TS2554: Expected 2 arguments, but got 1.
core/webview/ClineProvider.ts(685,31): error TS2345: Argument of type '[ExtensionMessage]' is not assignable to parameter of type 'never'.
core/webview/ClineProvider.ts(1204,55): error TS2341: Property 'load' is private and only accessible within class 'ProviderSettingsManager'.
core/webview/ClineProvider.ts(1316,23): error TS2339: Property 'apiKey' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; id?: string | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | ... 14 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/webview/webviewMessageHandler.ts(122,70): error TS2339: Property 'getCurrentConfig' does not exist on type 'ProviderSettingsManager'.
core/webview/webviewMessageHandler.ts(1411,62): error TS2341: Property 'load' is private and only accessible within class 'ProviderSettingsManager'.
core/webview/webviewMessageHandler.ts(1424,68): error TS2339: Property 'getCurrentConfig' does not exist on type 'ProviderSettingsManager'.
core/webview/webviewMessageHandler.ts(1433,47): error TS2339: Property 'setCurrentConfig' does not exist on type 'ProviderSettingsManager'.
core/webview/webviewMessageHandler.ts(1450,73): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.
core/webview/webviewMessageHandler.ts(1727,46): error TS2341: Property 'usageTrackingService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1755,45): error TS2341: Property 'trialManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1829,27): error TS2341: Property 'userManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1831,47): error TS2341: Property 'userManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1844,42): error TS2341: Property 'trialManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1845,38): error TS2322: Type '"trialExtended"' is not assignable to type '"autoApprovalEnabled" | "browserToolEnabled" | "remoteBrowserEnabled" | "maxReadFileLine" | "codebaseIndexConfig" | "configuration" | "action" | "showHumanRelayDialog" | ... 50 more ... | "trackedChanges"'.
core/webview/webviewMessageHandler.ts(1847,45): error TS2341: Property 'trialManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1860,27): error TS2341: Property 'trialManagementService' is private and only accessible within class 'UserManagementIntegration'.
core/webview/webviewMessageHandler.ts(1871,8): error TS2678: Type '"showUsageDetails"' is not comparable to type '"currentApiConfigName" | "customInstructions" | "condensingApiConfigId" | "autoApprovalEnabled" | "alwaysAllowReadOnly" | "alwaysAllowReadOnlyOutsideWorkspace" | "alwaysAllowWrite" | ... 176 more ... | "resetFirstTimeSetup"'.
core/webview/webviewMessageHandler.ts(2241,9): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
core/webview/webviewMessageHandler.ts(2282,9): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
core/webview/webviewMessageHandler.ts(2368,7): error TS2353: Object literal may only specify known properties, and 'messageTs' does not exist in type 'ExtensionMessage'.
core/webview/webviewMessageHandler.ts(2427,6): error TS2353: Object literal may only specify known properties, and 'messageTs' does not exist in type 'ExtensionMessage'.
core/webview/webviewMessageHandler.ts(2435,6): error TS2353: Object literal may only specify known properties, and 'messageTs' does not exist in type 'ExtensionMessage'.
core/webview/webviewMessageHandler.ts(2449,34): error TS2339: Property 'apiKey' does not exist on type '{ reasoningEffort?: "low" | "medium" | "high" | undefined; apiProvider?: "anthropic" | "glama" | "openrouter" | "bedrock" | "vertex" | "openai" | "ollama" | "vscode-lm" | "lmstudio" | ... 12 more ... | undefined; ... 72 more ...; codeIndexQdrantApiKey?: string | undefined; }'.
core/webview/webviewMessageHandler.ts(2462,6): error TS2322: Type '"byokApiKeysResponse"' is not assignable to type '"autoApprovalEnabled" | "browserToolEnabled" | "remoteBrowserEnabled" | "maxReadFileLine" | "codebaseIndexConfig" | "configuration" | "action" | "showHumanRelayDialog" | ... 50 more ... | "trackedChanges"'.
core/webview/webviewMessageHandler.ts(2468,6): error TS2322: Type '"byokApiKeysResponse"' is not assignable to type '"autoApprovalEnabled" | "browserToolEnabled" | "remoteBrowserEnabled" | "maxReadFileLine" | "codebaseIndexConfig" | "configuration" | "action" | "showHumanRelayDialog" | ... 50 more ... | "trackedChanges"'.
core/webview/webviewMessageHandler.ts(2651,16): error TS2339: Property 'cloudService' does not exist on type 'ClineProvider'.
core/webview/webviewMessageHandler.ts(2652,16): error TS2339: Property 'telemetryService' does not exist on type 'ClineProvider'.
extension.ts(429,8): error TS18048: 'autocompleteProvider' is possibly 'undefined'.
services/AuthenticationService.ts(220,5): error TS2353: Object literal may only specify known properties, and 'updatedAt' does not exist in type 'UserSession'.
services/AuthenticationService.ts(287,29): error TS2551: Property 'pictureUrl' does not exist on type 'CubentUser'. Did you mean 'picture'?
services/AuthenticationService.ts(304,31): error TS2339: Property 'trialEndDate' does not exist on type 'CubentUser'.
services/CubentWebDatabaseService.ts(95,35): error TS2559: Type 'Pool' has no properties in common with type 'PoolConfig'.
services/CubentWebDatabaseService.ts(98,37): error TS2353: Object literal may only specify known properties, and 'adapter' does not exist in type 'Subset<PrismaClientOptions, PrismaClientOptions>'.
services/CubentWebDatabaseService.ts(138,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(139,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(144,4): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/CubentWebDatabaseService.ts(147,4): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/CubentWebDatabaseService.ts(168,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(169,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(174,4): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/CubentWebDatabaseService.ts(177,4): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/CubentWebDatabaseService.ts(284,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(285,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/CubentWebDatabaseService.ts(286,4): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(113,35): error TS2559: Type 'Pool' has no properties in common with type 'PoolConfig'.
services/NeonDatabaseService.ts(117,5): error TS2322: Type 'PrismaNeon' is not assignable to type 'never'.
services/NeonDatabaseService.ts(171,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(172,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(173,27): error TS2339: Property 'extensionApiKey' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(174,24): error TS2339: Property 'sessionToken' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(175,29): error TS2339: Property 'lastExtensionSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(176,28): error TS2339: Property 'lastSettingsSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(178,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(180,27): error TS2551: Property 'termsAcceptedAt' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'. Did you mean 'termsAccepted'?
services/NeonDatabaseService.ts(185,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(186,29): error TS2339: Property 'extensionSettings' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(187,23): error TS2339: Property 'preferences' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(212,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(213,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(214,27): error TS2339: Property 'extensionApiKey' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(215,24): error TS2339: Property 'sessionToken' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(216,29): error TS2339: Property 'lastExtensionSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(217,28): error TS2339: Property 'lastSettingsSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(219,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(221,27): error TS2551: Property 'termsAcceptedAt' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'. Did you mean 'termsAccepted'?
services/NeonDatabaseService.ts(226,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(227,29): error TS2339: Property 'extensionSettings' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(228,23): error TS2339: Property 'preferences' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(268,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(269,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(270,27): error TS2339: Property 'extensionApiKey' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(271,24): error TS2339: Property 'sessionToken' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(272,29): error TS2339: Property 'lastExtensionSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(273,28): error TS2339: Property 'lastSettingsSync' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(275,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(277,27): error TS2551: Property 'termsAcceptedAt' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'. Did you mean 'termsAccepted'?
services/NeonDatabaseService.ts(282,5): error TS2322: Type 'Date | null' is not assignable to type 'Date | undefined'.
  Type 'null' is not assignable to type 'Date | undefined'.
services/NeonDatabaseService.ts(283,29): error TS2339: Property 'extensionSettings' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(284,23): error TS2339: Property 'preferences' does not exist on type '{ name: string | null; id: string; email: string; picture: string | null; subscriptionTier: string; subscriptionStatus: string; createdAt: Date; updatedAt: Date; lastActiveAt: Date | null; ... 5 more ...; termsAccepted: boolean; }'.
services/NeonDatabaseService.ts(302,6): error TS2353: Object literal may only specify known properties, and 'inputTokens' does not exist in type 'Without<UsageAnalyticsCreateInput, UsageAnalyticsUncheckedCreateInput> & UsageAnalyticsUncheckedCreateInput'.
services/NeonDatabaseService.ts(398,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(399,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/NeonDatabaseService.ts(400,5): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
services/UsageTrackingService.ts(135,45): error TS2339: Property 'cacheReadTokens' does not exist on type 'TokenUsage'.
services/UsageTrackingService.ts(136,46): error TS2339: Property 'cacheWriteTokens' does not exist on type 'TokenUsage'.
services/UsageTrackingService.ts(162,16): error TS2304: Cannot find name 'usageEntry'.
services/UsageTrackingService.ts(288,66): error TS2345: Argument of type '"warning" | "critical" | "exceeded"' is not assignable to parameter of type '"warning" | "exceeded" | "reset"'.
  Type '"critical"' is not assignable to type '"warning" | "exceeded" | "reset"'.
 ELIFECYCLE  Command failed with exit code 2.
