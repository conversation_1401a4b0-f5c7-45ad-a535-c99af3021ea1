export function getAttemptCompletionDescription(): string {
	return `## attempt_completion
Description: After each tool use, the user will respond with the result of that tool use, i.e. if it succeeded or failed, along with any reasons for failure. Once you've received the results of tool uses and can confirm that the task is complete, use this tool to signal task completion.

IMPORTANT: This tool ONLY signals completion - NO MESSAGE is shown to the user. Only feedback icons (thumbs up/down, copy, etc.) appear. Do NOT write explanatory text in the result parameter as it will not be displayed.

Optionally you may provide a CLI command to showcase the result of your work. The user may respond with feedback if they are not satisfied with the result, which you can use to make improvements and try again.

IMPORTANT NOTE: This tool CANNOT be used until you've confirmed from the user that any previous tool uses were successful. Failure to do so will result in code corruption and system failure. Before using this tool, you must ask yourself in <thinking></thinking> tags if you've confirmed from the user that any previous tool uses were successful. If not, then DO NOT use this tool.
Parameters:
- result: (required) A brief internal summary for tracking purposes only. This text is NEVER shown to the user - only feedback icons appear. Keep it short and factual. Do not write explanatory text or user-facing messages here.
- command: (optional) A CLI command to execute to show a live demo of the result to the user. For example, use \`open index.html\` to display a created html website, or \`open localhost:3000\` to display a locally running development server. But DO NOT use commands like \`echo\` or \`cat\` that merely print text. This command should be valid for the current operating system. Ensure the command is properly formatted and does not contain any harmful instructions.
Usage:
<attempt_completion>
<result>
Brief internal summary (NEVER shown to user)
</result>
<command>Command to demonstrate result (optional)</command>
</attempt_completion>

Example: Signaling task completion (NO message shown to user)
<attempt_completion>
<result>
Updated CSS styles
</result>
<command>open index.html</command>
</attempt_completion>

CRITICAL: The user sees ONLY feedback icons and optional command output. NO result text is ever displayed. Do not write user-facing explanations in the result parameter.`
}
